//+------------------------------------------------------------------+
//|                                                      Defines.mqh |
//|                                  动态概率统计分析系统核心定义文件 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef DEFINES_MQH
#define DEFINES_MQH

//+------------------------------------------------------------------+
//| 交易时段枚举                                                     |
//+------------------------------------------------------------------+
enum ENUM_TRADING_SESSION
  {
   SESSION_NONE,               // 休市时段
   SESSION_ASIA,               // 亚盘
   SESSION_EUROPE,             // 欧盘
   SESSION_US_EUROPE_CROSS,    // 欧美交叉
   SESSION_US_NIGHT            // 美盘后半夜
  };

//+------------------------------------------------------------------+
//| 日度数据结构体                                                   |
//+------------------------------------------------------------------+
struct HistoricalDailyData
  {
   datetime date_start_of_day; // 当日零点时间戳
   double   open;              // 开盘价
   double   high;              // 最高价
   double   low;               // 最低价
   double   close;             // 收盘价
   int      day_of_week;       // 星期几 (1=周一, 7=周日)
   int      month_of_year;     // 月份 (1-12)
  };

//+------------------------------------------------------------------+
//| 统计结果结构体                                                   |
//+------------------------------------------------------------------+
struct StatisticalResult
  {
   int      total_samples;       // 总样本数
   int      win_samples;         // 上涨/成功样本数
   double   win_rate;            // 胜率 (0.0-1.0)
   double   total_volatility_pips; // 累计总波幅 (点数)
   double   avg_volatility_pips; // 平均波幅 (点数)
  };

#endif // DEFINES_MQH