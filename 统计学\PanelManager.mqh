//+------------------------------------------------------------------+
//|                                                 PanelManager.mqh |
//|                                  动态概率统计分析系统UI面板管理器 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef PANEL_MANAGER_MQH
#define PANEL_MANAGER_MQH

#include "Defines.mqh"
#include <ChartObjects\ChartObjectsLabels.mqh>

//+------------------------------------------------------------------+
//| 面板配置常量                                                     |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     280
#define PANEL_HEIGHT    400
#define LINE_HEIGHT     18
#define LEFT_MARGIN     8
#define TOP_MARGIN      8

//+------------------------------------------------------------------+
//| 获取对象前缀                                                     |
//+------------------------------------------------------------------+
string GetObjectPrefix(const long chart_id)
  {
   return "ProbStats_" + (string)chart_id + "_";
  }

//+------------------------------------------------------------------+
//| 创建面板                                                         |
//+------------------------------------------------------------------+
void Panel_Create(const long chart_id, const int corner, const int x_offset, const int y_offset)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 创建主面板背景
   CChartObjectRectLabel *panel = new CChartObjectRectLabel();
   if(panel.Create(chart_id, prefix + "MainPanel", 0, x_offset, y_offset, PANEL_WIDTH, PANEL_HEIGHT))
     {
      panel.BackColor(clrBlack);
      panel.Color(clrWhite);
      panel.Corner((ENUM_BASE_CORNER)corner);
      panel.Style(STYLE_SOLID);
      panel.Width(1);
     }
   delete panel;
   
   int current_y = TOP_MARGIN;
   
   // 标题
   CChartObjectLabel *title = new CChartObjectLabel();
   if(title.Create(chart_id, prefix + "Title", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      title.Description("动态概率统计分析器 v2.0");
      title.FontSize(12);
      title.Color(clrYellow);
      title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete title;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator1 = new CChartObjectLabel();
   if(separator1.Create(chart_id, prefix + "Separator1", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator1.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      title.FontSize(8);
      separator1.Color(clrGray);
      separator1.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator1;
   current_y += LINE_HEIGHT;
   
   // 当前时间信息
   CChartObjectLabel *time_label = new CChartObjectLabel();
   if(time_label.Create(chart_id, prefix + "TimeLabel", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      time_label.Description("当前北京时间: --:--:--");
      time_label.FontSize(9);
      time_label.Color(clrWhite);
      time_label.Corner((ENUM_BASE_CORNER)corner);
     }
   delete time_label;
   current_y += LINE_HEIGHT;
   
   // 当前交易时段
   CChartObjectLabel *session_label = new CChartObjectLabel();
   if(session_label.Create(chart_id, prefix + "SessionLabel", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      session_label.Description("当前交易时段: 检测中...");
      session_label.FontSize(9);
      session_label.Color(clrLightBlue);
      session_label.Corner((ENUM_BASE_CORNER)corner);
     }
   delete session_label;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator2 = new CChartObjectLabel();
   if(separator2.Create(chart_id, prefix + "Separator2", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator2.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator2.FontSize(8);
      separator2.Color(clrGray);
      separator2.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator2;
   current_y += LINE_HEIGHT;
   
   // 当前时段统计
   CChartObjectLabel *current_stats_title = new CChartObjectLabel();
   if(current_stats_title.Create(chart_id, prefix + "CurrentStatsTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_stats_title.Description("【当前时段统计】");
      current_stats_title.FontSize(10);
      current_stats_title.Color(clrYellow);
      current_stats_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_stats_title;
   current_y += LINE_HEIGHT;
   
   // 当前时段胜率
   CChartObjectLabel *current_winrate = new CChartObjectLabel();
   if(current_winrate.Create(chart_id, prefix + "CurrentWinRate", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_winrate.Description("胜率: --%");
      current_winrate.FontSize(9);
      current_winrate.Color(clrWhite);
      current_winrate.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_winrate;
   current_y += LINE_HEIGHT;
   
   // 当前时段样本数
   CChartObjectLabel *current_samples = new CChartObjectLabel();
   if(current_samples.Create(chart_id, prefix + "CurrentSamples", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_samples.Description("样本数: --");
      current_samples.FontSize(9);
      current_samples.Color(clrWhite);
      current_samples.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_samples;
   current_y += LINE_HEIGHT;
   
   // 当前时段平均波幅
   CChartObjectLabel *current_volatility = new CChartObjectLabel();
   if(current_volatility.Create(chart_id, prefix + "CurrentVolatility", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_volatility.Description("平均波幅: -- pips");
      current_volatility.FontSize(9);
      current_volatility.Color(clrWhite);
      current_volatility.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_volatility;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator3 = new CChartObjectLabel();
   if(separator3.Create(chart_id, prefix + "Separator3", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator3.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator3.FontSize(8);
      separator3.Color(clrGray);
      separator3.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator3;
   current_y += LINE_HEIGHT;
   
   // 历史统计概览
   CChartObjectLabel *history_title = new CChartObjectLabel();
   if(history_title.Create(chart_id, prefix + "HistoryTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      history_title.Description("【历史统计概览】");
      history_title.FontSize(10);
      history_title.Color(clrYellow);
      history_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete history_title;
   current_y += LINE_HEIGHT;
   
   // 分析天数
   CChartObjectLabel *analysis_days = new CChartObjectLabel();
   if(analysis_days.Create(chart_id, prefix + "AnalysisDays", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      analysis_days.Description("分析天数: --");
      analysis_days.FontSize(9);
      analysis_days.Color(clrWhite);
      analysis_days.Corner((ENUM_BASE_CORNER)corner);
     }
   delete analysis_days;
   current_y += LINE_HEIGHT;
   
   // 总样本数
   CChartObjectLabel *total_samples = new CChartObjectLabel();
   if(total_samples.Create(chart_id, prefix + "TotalSamples", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      total_samples.Description("总样本数: --");
      total_samples.FontSize(9);
      total_samples.Color(clrWhite);
      total_samples.Corner((ENUM_BASE_CORNER)corner);
     }
   delete total_samples;
   current_y += LINE_HEIGHT;
   
   // 整体胜率
   CChartObjectLabel *overall_winrate = new CChartObjectLabel();
   if(overall_winrate.Create(chart_id, prefix + "OverallWinRate", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      overall_winrate.Description("整体胜率: --%");
      overall_winrate.FontSize(9);
      overall_winrate.Color(clrLightGreen);
      overall_winrate.Corner((ENUM_BASE_CORNER)corner);
     }
   delete overall_winrate;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator4 = new CChartObjectLabel();
   if(separator4.Create(chart_id, prefix + "Separator4", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator4.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator4.FontSize(8);
      separator4.Color(clrGray);
      separator4.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator4;
   current_y += LINE_HEIGHT;
   
   // 状态信息
   CChartObjectLabel *status_label = new CChartObjectLabel();
   if(status_label.Create(chart_id, prefix + "StatusLabel", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      status_label.Description("状态: 初始化中...");
      status_label.FontSize(9);
      status_label.Color(clrOrange);
      status_label.Corner((ENUM_BASE_CORNER)corner);
     }
   delete status_label;
   current_y += LINE_HEIGHT;
   
   // 最后更新时间
   CChartObjectLabel *update_time = new CChartObjectLabel();
   if(update_time.Create(chart_id, prefix + "UpdateTime", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      update_time.Description("更新: --:--:--");
      update_time.FontSize(8);
      update_time.Color(clrGray);
      update_time.Corner((ENUM_BASE_CORNER)corner);
     }
   delete update_time;
   
   Print("UI面板创建完成");
  }

//+------------------------------------------------------------------+
//| 更新静态信息                                                     |
//+------------------------------------------------------------------+
void Panel_UpdateStaticInfo(const long chart_id, const int sample_days)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 更新分析天数
   ObjectSetString(chart_id, prefix + "AnalysisDays", OBJPROP_TEXT, "分析样本: " + (string)sample_days + "天");
   
   // 刷新图表
   ChartRedraw(chart_id);
  }

//+------------------------------------------------------------------+
//| 更新面板                                                         |
//+------------------------------------------------------------------+
void Panel_Update(const long chart_id, const string beijing_time, const string session_name, const StatisticalResult &stats)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 更新北京时间
   ObjectSetString(chart_id, prefix + "TimeLabel", OBJPROP_TEXT, "当前北京时间: " + beijing_time);
   
   // 更新交易时段
   ObjectSetString(chart_id, prefix + "SessionLabel", OBJPROP_TEXT, "当前交易时段: " + session_name);
   
   // 更新当前时段统计数据
   string winrate_text = "胜率: ";
   if(stats.total_samples > 0)
     {
      winrate_text += StringFormat("%.1f%%", stats.win_rate * 100.0);
     }
   else
     {
      winrate_text += "--";
     }
   ObjectSetString(chart_id, prefix + "CurrentWinRate", OBJPROP_TEXT, winrate_text);
   
   // 更新样本数
   string samples_text = "样本数: ";
   if(stats.total_samples > 0)
     {
      samples_text += (string)stats.total_samples;
     }
   else
     {
      samples_text += "--";
     }
   ObjectSetString(chart_id, prefix + "CurrentSamples", OBJPROP_TEXT, samples_text);
   
   // 更新平均波幅
   string volatility_text = "平均波幅: ";
   if(stats.total_samples > 0)
     {
      volatility_text += StringFormat("%.1f pips", stats.avg_volatility_pips);
     }
   else
     {
      volatility_text += "-- pips";
     }
   ObjectSetString(chart_id, prefix + "CurrentVolatility", OBJPROP_TEXT, volatility_text);
   
   // 更新最后更新时间
   ObjectSetString(chart_id, prefix + "UpdateTime", OBJPROP_TEXT, "更新: " + beijing_time);
   
   // 刷新图表
   ChartRedraw(chart_id);
  }

//+------------------------------------------------------------------+
//| 删除面板                                                         |
//+------------------------------------------------------------------+
void Panel_Delete(const long chart_id)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 删除所有相关对象
   ObjectsDeleteAll(chart_id, prefix);
   
   Print("UI面板已清理");
  }

#endif // PANEL_MANAGER_MQH